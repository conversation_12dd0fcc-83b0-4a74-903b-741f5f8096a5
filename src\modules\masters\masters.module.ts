import { Module } from '@nestjs/common';
import { CitiesModule } from './cities/cities.module';
import { LocationsModule } from './locations/locations.module';
import { AmenitiesModule } from './amenities/amenities.module';
import { FloorsModule } from './floors/floors.module';
import { TowersModule } from './towers/towers.module';
import { PropertyTypesModule } from './property-types/property-types.module';
// Add other master submodules as you create them (e.g., RoomsModule, WashroomsModule)

@Module({
  imports: [
    CitiesModule,
    LocationsModule,
    AmenitiesModule,
    FloorsModule,
    TowersModule,
    PropertyTypesModule,
    // RoomsModule,
    // WashroomsModule,
  ],
  exports: [
    CitiesModule,
    LocationsModule,
    AmenitiesModule,
    FloorsModule,
    TowersModule,
    PropertyTypesModule,
    // RoomsModule,
    // WashroomsModule,
  ],
})
export class MastersModule {} 
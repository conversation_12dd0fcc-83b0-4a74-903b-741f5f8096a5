import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON><PERSON><PERSON>, Min<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * Data Transfer Object for user login
 * Validates email and password input for authentication
 */
export class LoginDto {
  @ApiProperty({
    description: 'Admin email address - Use one of the predefined admin accounts',
    example: '<EMAIL>',
    format: 'email',
    examples: {
      admin: {
        value: '<EMAIL>',
        description: 'Standard admin account'
      },
      superadmin: {
        value: '<EMAIL>',
        description: 'Super admin account with full privileges'
      },
      manager: {
        value: '<EMAIL>',
        description: 'Manager admin account'
      }
    }
  })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({
    description: 'Admin password - Default password for all admin accounts is "admin123"',
    example: 'admin123',
    minLength: 6,
  })
  @IsString({ message: 'Password must be a string' })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;
}

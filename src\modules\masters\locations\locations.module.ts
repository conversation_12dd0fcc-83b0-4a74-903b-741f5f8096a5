import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { LocationsController } from './locations.controller';
import { LocationsService } from './locations.service';
import { Location, LocationSchema } from './schemas/location.schema';

/**
 * Locations Module
 * Handles location master data management
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Location.name, schema: LocationSchema }
    ])
  ],
  controllers: [LocationsController],
  providers: [LocationsService],
  exports: [LocationsService]
})
export class LocationsModule {}

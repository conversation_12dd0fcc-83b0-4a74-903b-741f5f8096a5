import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AmenitiesController } from './amenities.controller';
import { AmenitiesService } from './amenities.service';
import { Amenity, AmenitySchema } from './schemas/amenity.schema';

/**
 * Amenities Module
 * Handles amenity master data management
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Amenity.name, schema: AmenitySchema }
    ])
  ],
  controllers: [AmenitiesController],
  providers: [AmenitiesService],
  exports: [AmenitiesService]
})
export class AmenitiesModule {}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsNotEmpty, 
  IsOptional, 
  IsNumber, 
  IsArray, 
  IsObject,
  IsBoolean,
  IsEnum,
  Min,
  Max,
  Max<PERSON>ength,
  ValidateNested,
  IsUrl,
  IsMongoId
} from 'class-validator';
import { Type } from 'class-transformer';
import { MasterWithParentDto } from '../../common/dto/base-master.dto';
import { MasterType } from '../../common/enums/master-types.enum';

/**
 * Connectivity Data DTO
 */
export class ConnectivityDataDto {
  @ApiPropertyOptional({ 
    description: 'Nearest metro station name',
    example: 'Bandra Station'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  nearestMetroStation?: string;

  @ApiPropertyOptional({ 
    description: 'Distance to nearest metro station in km',
    example: 2.5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  metroDistance?: number;

  @ApiPropertyOptional({ 
    description: 'Nearest railway station name',
    example: 'Bandra Railway Station'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  nearestRailwayStation?: string;

  @ApiPropertyOptional({ 
    description: 'Distance to nearest railway station in km',
    example: 1.2
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  railwayDistance?: number;

  @ApiPropertyOptional({ 
    description: 'Nearest airport name',
    example: 'Chhatrapati Shivaji International Airport'
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  nearestAirport?: string;

  @ApiPropertyOptional({ 
    description: 'Distance to nearest airport in km',
    example: 15.5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  airportDistance?: number;

  @ApiPropertyOptional({ 
    description: 'Major roads connecting the location',
    example: ['Western Express Highway', 'Linking Road']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  majorRoads?: string[];

  @ApiPropertyOptional({ 
    description: 'Bus connectivity options',
    example: ['BEST Bus', 'Private Bus Services']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  busConnectivity?: string[];
}

/**
 * Price Range DTO
 */
export class PriceRangeDto {
  @ApiProperty({ 
    description: 'Minimum price per sq ft',
    example: 8000
  })
  @IsNumber()
  @Min(0)
  min: number;

  @ApiProperty({ 
    description: 'Maximum price per sq ft',
    example: 15000
  })
  @IsNumber()
  @Min(0)
  max: number;
}

/**
 * Real Estate Data DTO
 */
export class RealEstateDataDto {
  @ApiPropertyOptional({ 
    description: 'Average property price per sq ft',
    example: 12000
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  averagePropertyPrice?: number;

  @ApiPropertyOptional({ 
    description: 'Price range',
    type: PriceRangeDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PriceRangeDto)
  priceRange?: PriceRangeDto;

  @ApiPropertyOptional({ 
    description: 'Price appreciation rate percentage',
    example: 8.5
  })
  @IsOptional()
  @IsNumber()
  @Min(-100)
  @Max(100)
  priceAppreciationRate?: number;

  @ApiPropertyOptional({ 
    description: 'Rental yield percentage',
    example: 3.5
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  rentalYield?: number;

  @ApiPropertyOptional({ 
    description: 'Popular property types',
    example: ['apartment', 'villa', 'penthouse']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  popularPropertyTypes?: string[];

  @ApiPropertyOptional({ 
    description: 'Number of upcoming projects',
    example: 12
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  upcomingProjects?: number;

  @ApiPropertyOptional({ 
    description: 'Total number of projects',
    example: 45
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalProjects?: number;

  @ApiPropertyOptional({ 
    description: 'Occupancy rate percentage',
    example: 85
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  occupancyRate?: number;
}

/**
 * Nearby Facilities DTO
 */
export class NearbyFacilitiesDto {
  @ApiPropertyOptional({ 
    description: 'Nearby schools',
    example: ['St. Andrews High School', 'Bandra Public School']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  schools?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby hospitals',
    example: ['Lilavati Hospital', 'Holy Family Hospital']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  hospitals?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby shopping malls',
    example: ['Palladium Mall', 'Linking Road Market']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  shoppingMalls?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby restaurants',
    example: ['Trishna', 'Mahesh Lunch Home']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  restaurants?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby banks',
    example: ['HDFC Bank', 'ICICI Bank']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  banks?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby parks',
    example: ['Bandra Bandstand', 'Carter Road Promenade']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parks?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby gyms',
    example: ['Gold\'s Gym', 'Fitness First']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  gyms?: string[];

  @ApiPropertyOptional({ 
    description: 'Nearby temples',
    example: ['Mount Mary Church', 'Mahim Dargah']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  temples?: string[];
}

/**
 * Create Location DTO
 * Extends MasterWithParentDto with location-specific fields
 */
export class CreateLocationDto extends MasterWithParentDto {
  @ApiProperty({ 
    description: 'Location name',
    example: 'Bandra West',
    maxLength: 100
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ 
    description: 'Location description',
    example: 'Premium residential and commercial area in Mumbai',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Unique location code',
    example: 'BW',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  code?: string;

  @ApiProperty({ 
    description: 'City ID (parent)',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  parentId: string;

  @ApiPropertyOptional({ 
    description: 'Location code within the city',
    example: 'BW001',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  locationCode?: string;

  @ApiPropertyOptional({ 
    description: 'Coordinates [longitude, latitude]',
    example: [72.8265, 19.0596]
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  coordinates?: [number, number];

  @ApiPropertyOptional({ 
    description: 'Full address',
    example: 'Bandra West, Mumbai, Maharashtra 400050',
    maxLength: 200
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  address?: string;

  @ApiPropertyOptional({ 
    description: 'Pincode',
    example: '400050',
    maxLength: 20
  })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  pincode?: string;

  @ApiPropertyOptional({ 
    description: 'Nearby landmark',
    example: 'Near Bandra Bandstand',
    maxLength: 100
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  landmark?: string;

  @ApiPropertyOptional({ 
    description: 'Alternative names',
    example: ['Bandra (W)', 'West Bandra']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  alternateNames?: string[];

  @ApiPropertyOptional({ 
    description: 'Location type',
    example: 'residential',
    enum: ['residential', 'commercial', 'mixed', 'industrial', 'it_hub', 'business_district', 'suburb', 'downtown', 'waterfront', 'hillside']
  })
  @IsOptional()
  @IsEnum(['residential', 'commercial', 'mixed', 'industrial', 'it_hub', 'business_district', 'suburb', 'downtown', 'waterfront', 'hillside'])
  locationType?: string;

  @ApiPropertyOptional({ 
    description: 'Location category',
    example: 'premium',
    enum: ['prime', 'premium', 'mid_range', 'budget', 'luxury', 'affordable', 'upcoming', 'established']
  })
  @IsOptional()
  @IsEnum(['prime', 'premium', 'mid_range', 'budget', 'luxury', 'affordable', 'upcoming', 'established'])
  locationCategory?: string;

  @ApiPropertyOptional({ 
    description: 'Connectivity information',
    type: ConnectivityDataDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ConnectivityDataDto)
  connectivity?: ConnectivityDataDto;

  @ApiPropertyOptional({ 
    description: 'Real estate data',
    type: RealEstateDataDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RealEstateDataDto)
  realEstateData?: RealEstateDataDto;

  @ApiPropertyOptional({ 
    description: 'Nearby facilities',
    type: NearbyFacilitiesDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => NearbyFacilitiesDto)
  nearbyFacilities?: NearbyFacilitiesDto;

  @ApiPropertyOptional({ 
    description: 'SEO title',
    example: 'Bandra West Mumbai - Premium Real Estate Location',
    maxLength: 200
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  seoTitle?: string;

  @ApiPropertyOptional({ 
    description: 'SEO description',
    example: 'Discover premium properties in Bandra West, Mumbai\'s most sought-after residential area.',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  seoDescription?: string;

  @ApiPropertyOptional({ 
    description: 'SEO keywords',
    example: ['bandra west properties', 'mumbai real estate', 'premium apartments bandra']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  seoKeywords?: string[];

  @ApiPropertyOptional({ 
    description: 'Featured image URL',
    example: 'https://s3.amazonaws.com/bucket/bandra-west.jpg'
  })
  @IsOptional()
  @IsString()
  @IsUrl()
  featuredImage?: string;

  @ApiPropertyOptional({ 
    description: 'Gallery image URLs',
    example: ['https://s3.amazonaws.com/bucket/bandra-1.jpg', 'https://s3.amazonaws.com/bucket/bandra-2.jpg']
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  gallery?: string[];

  // Set masterType and parentType
  masterType: MasterType.LOCATION = MasterType.LOCATION;
  parentType: MasterType.CITY = MasterType.CITY;
}
